<?php

namespace Webkul\DtTheme;

use Webkul\Product\ProductImage as BaseProductImage;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\Local\LocalFilesystemAdapter;

class ProductImage extends BaseProductImage
{
    /**
     * Get cached urls configured for intervention package.
     *
     * @param  string  $path
     */
    protected function getCachedImageUrls($path): array
    {
        if (! $this->isDriverLocal()) {
            return [
                'small_image_url'        => Storage::url($path),
                'medium_image_url'       => Storage::url($path),
                'large_image_url'        => Storage::url($path),
                'canvas_square_image_url' => Storage::url($path),
                'original_image_url'     => Storage::url($path),
            ];
        }

        return [
            'small_image_url'        => url('cache/small/'.$path),
            'medium_image_url'       => url('cache/medium/'.$path),
            'large_image_url'        => url('cache/large/'.$path),
            'canvas_square_image_url' => url('cache/canvas-square/'.$path),
            'original_image_url'     => url('cache/original/'.$path),
        ];
    }

    /**
     * Get fallback urls.
     */
    protected function getFallbackImageUrls(): array
    {
        $smallImageUrl = core()->getConfigData('catalog.products.cache_small_image.url')
                        ? Storage::url(core()->getConfigData('catalog.products.cache_small_image.url'))
                        : bagisto_asset('images/small-product-placeholder.webp', 'shop');

        $mediumImageUrl = core()->getConfigData('catalog.products.cache_medium_image.url')
                        ? Storage::url(core()->getConfigData('catalog.products.cache_medium_image.url'))
                        : bagisto_asset('images/medium-product-placeholder.webp', 'shop');

        $largeImageUrl = core()->getConfigData('catalog.products.cache_large_image.url')
                        ? Storage::url(core()->getConfigData('catalog.products.cache_large_image.url'))
                        : bagisto_asset('images/large-product-placeholder.webp', 'shop');

        return [
            'small_image_url'        => $smallImageUrl,
            'medium_image_url'       => $mediumImageUrl,
            'large_image_url'        => $largeImageUrl,
            'canvas_square_image_url' => bagisto_asset('images/large-product-placeholder.webp', 'shop'),
            'original_image_url'     => bagisto_asset('images/large-product-placeholder.webp', 'shop'),
        ];
    }
}

<?php

namespace Webkul\DtTheme\CacheFilters;

use Intervention\Image\Filters\FilterInterface;
use Intervention\Image\Image;

// used for the product detail screen

class CanvasSquare implements FilterInterface
{
    /**
     * Apply filter - Creates canvas with white background
     *
     * @return \Intervention\Image\Image
     */
    public function applyFilter(Image $image)
    {
        return $this->resizeCanvas($image, 865, 955, '#ffffff');
    }

    /**
     * Resize canvas to fixed size while keeping image intact
     *
     * @param \Intervention\Image\Image $image
     * @param int $width
     * @param int $height
     * @param string $backgroundColor
     * @return \Intervention\Image\Image
     */
    private function resizeCanvas(Image $image, int $width, int $height, string $backgroundColor = '#ffffff')
    {
        // Get original dimensions
        $originalWidth = $image->width();
        $originalHeight = $image->height();

        // Calculate scaling to fit within canvas while maintaining aspect ratio
        $scaleX = $width / $originalWidth;
        $scaleY = $height / $originalHeight;
        $scale = min($scaleX, $scaleY);

        // Calculate new dimensions
        $newWidth = (int) ($originalWidth * $scale);
        $newHeight = (int) ($originalHeight * $scale);

        // Resize image proportionally
        $image->resize($newWidth, $newHeight);

        // Create canvas with background color
        $canvas = $image->getDriver()->newImage($width, $height, $backgroundColor);

        // Calculate position to center the image
        $x = (int) (($width - $newWidth) / 2);
        $y = (int) (($height - $newHeight) / 2);

        // Insert resized image onto canvas
        $canvas->insert($image, 'top-left', $x, $y);

        return $canvas;
    }
}

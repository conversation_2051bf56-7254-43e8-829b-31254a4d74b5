<?php return array (
  'astrotomic/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'bagisto/laravel-datafaker' => 
  array (
    'aliases' => 
    array (
    ),
    'providers' => 
    array (
      0 => 'Webkul\\Faker\\Providers\\FakerServiceProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'diglactic/laravel-breadcrumbs' => 
  array (
    'aliases' => 
    array (
      'Breadcrumbs' => 'Diglactic\\Breadcrumbs\\Breadcrumbs',
    ),
    'providers' => 
    array (
      0 => 'Diglactic\\Breadcrumbs\\ServiceProvider',
    ),
  ),
  'kalnoy/nestedset' => 
  array (
    'providers' => 
    array (
      0 => 'Kalnoy\\Nestedset\\NestedSetServiceProvider',
    ),
  ),
  'konekt/concord' => 
  array (
    'aliases' => 
    array (
      'Helper' => 'Konekt\\Concord\\Facades\\Helper',
      'Concord' => 'Konekt\\Concord\\Facades\\Concord',
    ),
    'providers' => 
    array (
      0 => 'Konekt\\Concord\\ConcordServiceProvider',
    ),
  ),
  'konekt/enum-eloquent' => 
  array (
    'providers' => 
    array (
      0 => 'Konekt\\Enum\\Eloquent\\EnumServiceProvider',
    ),
  ),
  'laravel/octane' => 
  array (
    'aliases' => 
    array (
      'Octane' => 'Laravel\\Octane\\Facades\\Octane',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Octane\\OctaneServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'openai-php/laravel' => 
  array (
    'providers' => 
    array (
      0 => 'OpenAI\\Laravel\\ServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'prettus/l5-repository' => 
  array (
    'providers' => 
    array (
      0 => 'Prettus\\Repository\\Providers\\RepositoryServiceProvider',
    ),
  ),
  'spatie/laravel-responsecache' => 
  array (
    'aliases' => 
    array (
      'ResponseCache' => 'Spatie\\ResponseCache\\Facades\\ResponseCache',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\ResponseCache\\ResponseCacheServiceProvider',
    ),
  ),
  'spatie/laravel-sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    ),
  ),
);
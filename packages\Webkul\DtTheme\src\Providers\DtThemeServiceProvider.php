<?php
// andrew
namespace Webkul\DtTheme\Providers;

use Illuminate\Support\ServiceProvider;

class DtThemeServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->publishes([
            __DIR__ . '/../Resources/views'  => resource_path('themes/dt-theme/views'),
        ]);

        // Load custom translations
        $this->loadTranslationsFrom(__DIR__.'/../Resources/lang', 'dt-theme');

        // Register custom image cache filters
        $this->registerImageCacheFilters();

        // Register custom ProductImage class
        $this->registerCustomProductImage();

        // Override shop translations (optional - uncomment if you want to completely override shop translations)
        // $this->loadTranslationsFrom(__DIR__.'/../Resources/lang', 'shop');
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->registerConfig();
    }

    /**
     * Register package config.
     *
     * @return void
     */
    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/menu.php',
            'menu.customer'
        );
    }

    /**
     * Register custom image cache filters.
     *
     * @return void
     */
    protected function registerImageCacheFilters(): void
    {
        // Add custom image cache filters to the imagecache templates
        config([
            'imagecache.templates.extra-large' => 'Webkul\DtTheme\CacheFilters\ExtraLarge',
            'imagecache.templates.canvas-square' => 'Webkul\DtTheme\CacheFilters\CanvasSquare',
        ]);
    }

    /**
     * Register custom ProductImage class.
     *
     * @return void
     */
    protected function registerCustomProductImage(): void
    {
        // Bind the custom ProductImage class
        // $this->app->bind('product_image', \Webkul\DtTheme\ProductImage::class);
    }
}
